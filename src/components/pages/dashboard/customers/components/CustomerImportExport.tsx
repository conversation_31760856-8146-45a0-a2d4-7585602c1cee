"use client";

import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import {
  Download,
  Upload,
  FileSpreadsheet,
  FileText,
  AlertCircle,
  CheckCircle,
  Info,
  Calendar,
  Settings,
} from "lucide-react";
import * as XLSX from "xlsx-js-style";
import { toast } from "sonner";

interface ImportSummary {
  customersCreated: number;
  errors: string[];
}

interface ExportConfig {
  reportType: "harian" | "bulanan" | "tahunan";
  selectedDate: Date;
  selectedMonth: number;
  selectedYear: number;
  format: "excel" | "csv";
  includeSummary: boolean;
  includeCharts: boolean;
}

interface CustomerImportExportProps {
  onRefresh?: () => void; // Callback to refresh the customers list after import
}

// Function to create customer-only Excel report
const createCustomerOnlyExcelReport = (
  customers: any[],
  options: {
    reportTitle: string;
    includeSummary: boolean;
    totalCustomers: number;
  }
) => {
  const workbook = XLSX.utils.book_new();

  // Create header info
  const headerData = [
    [options.reportTitle],
    [`Diekspor pada: ${new Date().toLocaleString("id-ID")}`],
    [`Total Pelanggan: ${options.totalCustomers}`],
    [], // Empty row
  ];

  // Define column headers
  const columnHeaders = [
    "ID Pelanggan",
    "Nama Pelanggan",
    "Nama Depan",
    "Nama Tengah",
    "Nama Belakang",
    "Nama Kontak",
    "Telepon",
    "Email",
    "Jenis Identitas",
    "Nomor Identitas",
    "NIK",
    "NPWP",
    "Nama Perusahaan",
    "Alamat",
    "Alamat Penagihan",
    "Alamat Pengiriman",
    "Catatan",
  ];

  // Prepare customer data
  const customerData = customers.map((customer) => {
    return [
      customer.id || "-",
      customer.name || "-",
      customer.firstName || "-",
      customer.middleName || "-",
      customer.lastName || "-",
      customer.contactName || "-",
      customer.phone || "-",
      customer.email || "-",
      customer.identityType || "-",
      customer.identityNumber || "-",
      customer.NIK || "-",
      customer.NPWP || "-",
      customer.companyName || "-",
      customer.address || "-",
      customer.billingAddress || "-",
      customer.shippingAddress || "-",
      customer.notes || "-",
    ];
  });

  // Combine all data
  const worksheetData = [...headerData, columnHeaders, ...customerData];

  // Create worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

  // Set column widths
  const columnWidths = [
    { wch: 25 }, // ID Pelanggan
    { wch: 30 }, // Nama Pelanggan
    { wch: 20 }, // Nama Depan
    { wch: 20 }, // Nama Tengah
    { wch: 20 }, // Nama Belakang
    { wch: 25 }, // Nama Kontak
    { wch: 15 }, // Telepon
    { wch: 30 }, // Email
    { wch: 15 }, // Jenis Identitas
    { wch: 20 }, // Nomor Identitas
    { wch: 20 }, // NIK
    { wch: 20 }, // NPWP
    { wch: 30 }, // Nama Perusahaan
    { wch: 40 }, // Alamat
    { wch: 40 }, // Alamat Penagihan
    { wch: 40 }, // Alamat Pengiriman
    { wch: 50 }, // Catatan
  ];
  worksheet["!cols"] = columnWidths;

  // Style the header rows
  const headerStyle = {
    font: { bold: true, sz: 14 },
    alignment: { horizontal: "center" },
    fill: { fgColor: { rgb: "E3F2FD" } },
  };

  const columnHeaderStyle = {
    font: { bold: true, sz: 12 },
    alignment: { horizontal: "center" },
    fill: { fgColor: { rgb: "BBDEFB" } },
    border: {
      top: { style: "thin" },
      bottom: { style: "thin" },
      left: { style: "thin" },
      right: { style: "thin" },
    },
  };

  // Apply styles to header
  if (worksheet["A1"]) worksheet["A1"].s = headerStyle;
  if (worksheet["A2"]) worksheet["A2"].s = { font: { sz: 10 } };
  if (worksheet["A3"]) worksheet["A3"].s = { font: { sz: 10 } };

  // Apply styles to column headers (row 5)
  const headerRowIndex = 5;
  columnHeaders.forEach((_, colIndex) => {
    const cellAddress = XLSX.utils.encode_cell({
      r: headerRowIndex - 1,
      c: colIndex,
    });
    if (worksheet[cellAddress]) {
      worksheet[cellAddress].s = columnHeaderStyle;
    }
  });

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, "Data Pelanggan");

  return workbook;
};

export const CustomerImportExport: React.FC<CustomerImportExportProps> = ({
  onRefresh,
}) => {
  // Import states
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importSummary, setImportSummary] = useState<ImportSummary | null>(
    null
  );
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Export states
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportConfig, setExportConfig] = useState<ExportConfig>({
    reportType: "bulanan",
    selectedDate: new Date(),
    selectedMonth: new Date().getMonth(),
    selectedYear: new Date().getFullYear(),
    format: "excel",
    includeSummary: false,
    includeCharts: false,
  });

  // Download template function
  const downloadTemplate = () => {
    try {
      // Create a simple template for customers
      const workbook = XLSX.utils.book_new();
      
      // Template headers
      const headers = [
        "Nama Pelanggan*",
        "Nama Depan",
        "Nama Tengah", 
        "Nama Belakang",
        "Nama Kontak",
        "Telepon",
        "Email",
        "Jenis Identitas",
        "Nomor Identitas",
        "NIK",
        "NPWP",
        "Nama Perusahaan",
        "Alamat",
        "Alamat Penagihan",
        "Alamat Pengiriman",
        "Catatan"
      ];

      // Create worksheet with headers
      const worksheet = XLSX.utils.aoa_to_sheet([headers]);
      
      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, "Template Pelanggan");
      
      const fileName = `template-import-pelanggan-${new Date().toISOString().split("T")[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      toast.success("Template berhasil diunduh!");
    } catch (error) {
      console.error("Template download error:", error);
      toast.error("Gagal mengunduh template");
    }
  };

  // Handle import function
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("Ukuran file maksimal 10MB");
      return;
    }

    // Validate file type
    if (!file.name.match(/\.(xlsx|xls)$/i)) {
      toast.error("Format file harus Excel (.xlsx atau .xls)");
      return;
    }

    setIsImporting(true);
    setImportProgress(0);
    setImportSummary(null);

    try {
      setImportProgress(20);

      const arrayBuffer = await file.arrayBuffer();
      setImportProgress(40);

      // TODO: Implement customer import API call
      // const result = await importCustomers(arrayBuffer);
      setImportProgress(80);

      // Simulate success for now
      setImportProgress(100);
      setImportSummary({
        customersCreated: 0,
        errors: ["Import functionality will be implemented soon"]
      });
      toast.info("Import functionality will be implemented soon");

      // Auto-refresh data on successful import
      if (onRefresh) {
        setTimeout(() => {
          onRefresh();
        }, 1500);
      }
    } catch (error) {
      console.error("Import error:", error);
      toast.error("Gagal mengimpor file");
    } finally {
      setIsImporting(false);
      setImportProgress(0);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* Import Button and Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Import
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Import Data Pelanggan
            </DialogTitle>
            <DialogDescription>
              Import data pelanggan dari file Excel dengan mudah dan aman
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            {/* Instructions */}
            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div className="space-y-2">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100">
                    Cara Import Data Pelanggan:
                  </h4>
                  <ol className="text-sm text-blue-800 dark:text-blue-200 space-y-1 list-decimal list-inside">
                    <li>
                      Download template Excel dengan klik tombol &quot;Download
                      Template&quot;
                    </li>
                    <li>Isi data pelanggan sesuai format yang tersedia</li>
                    <li>Upload file Excel yang sudah diisi</li>
                    <li>Tunggu proses import selesai</li>
                  </ol>
                </div>
              </div>
            </div>

            {/* Download Template */}
            <div className="space-y-2">
              <h4 className="font-medium">1. Download Template</h4>
              <Button
                onClick={downloadTemplate}
                variant="outline"
                className="w-full flex items-center gap-2"
              >
                <FileSpreadsheet className="h-4 w-4" />
                Download Template Excel
              </Button>
            </div>

            <Separator />

            {/* File Upload */}
            <div className="space-y-2">
              <h4 className="font-medium">2. Upload File Excel</h4>
              <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center">
                <Upload className="h-12 w-12 mx-auto text-slate-400 mb-4" />
                <p className="text-sm text-slate-600 mb-2">
                  Klik untuk memilih file atau drag & drop
                </p>
                <p className="text-xs text-slate-500">
                  Format: .xlsx, .xls (Maksimal 10MB)
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleImport}
                  className="hidden"
                />
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="mt-4"
                  disabled={isImporting}
                >
                  {isImporting ? "Memproses..." : "Pilih File Excel"}
                </Button>
              </div>
            </div>

            {/* Import Progress */}
            {isImporting && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Mengimpor data pelanggan...</span>
                  <span>{importProgress}%</span>
                </div>
                <Progress value={importProgress} />
              </div>
            )}

            {/* Import Summary */}
            {importSummary && (
              <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  Hasil Import:
                </h4>
                <div className="text-sm space-y-1">
                  <p>
                    ✅ Pelanggan berhasil dibuat: {importSummary.customersCreated}
                  </p>

                  {importSummary.errors && importSummary.errors.length > 0 && (
                    <div className="mt-3">
                      <p className="text-red-600 font-medium flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        Error:
                      </p>
                      <div className="max-h-32 overflow-y-auto">
                        {importSummary.errors
                          .slice(0, 10)
                          .map((error: string, index: number) => (
                            <p key={index} className="text-xs text-red-600">
                              • {error}
                            </p>
                          ))}
                        {importSummary.errors.length > 10 && (
                          <p className="text-xs text-red-600">
                            ... dan {importSummary.errors.length - 10} error
                            lainnya
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Close Button */}
            {importSummary && (
              <div className="flex justify-end pt-4">
                <Button
                  onClick={() => {
                    setShowImportDialog(false);
                    setImportSummary(null);
                    if (fileInputRef.current) {
                      fileInputRef.current.value = "";
                    }
                  }}
                  className="flex items-center gap-2"
                >
                  Tutup
                </Button>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Export Button */}
      <Button 
        variant="outline" 
        className="flex items-center gap-2"
        onClick={() => toast.info("Export functionality will be implemented soon")}
      >
        <Download className="h-4 w-4" />
        Export
      </Button>
    </div>
  );
};
